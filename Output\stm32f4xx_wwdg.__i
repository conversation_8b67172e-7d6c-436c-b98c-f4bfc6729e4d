-c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\Libraries\CMSIS\Device\ST\STM32F4xx\Include -I ..\..\Libraries\STM32F4xx_StdPeriph_Driver\inc -I ..\..\Libraries\CMSIS\Include -I ..\..\User -I ..\..\User\tim_adc_dma_fft -I ..\..\User\usart -I ..\..\User\usart2
-I.\RTE\_ADVANCE_TIM
-IE:\Keil5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include
-IE:\Keil5\ARM\PACK\Keil\STM32F4xx_DFP\2.15.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include
-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F429xx -DUSE_STDPERIPH_DRIVER -DSTM32F429_439xx -DARM_MATH_CM4 -DARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING
-o ..\..\output\stm32f4xx_wwdg.o --omf_browse ..\..\output\stm32f4xx_wwdg.crf --depend ..\..\output\stm32f4xx_wwdg.d "..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_wwdg.c"
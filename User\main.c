/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V1.0
  * @date    2015-xx-xx
  * @brief   ͨ�ö�ʱ������ͬ��
  ******************************************************************************
  * @attention
  *
  * ʵ��ƽ̨:Ұ��  STM32 F429 ������
  * ��̳    :http://www.firebbs.cn
  * �Ա�    :https://fire-stm32.taobao.com
  *
  ******************************************************************************
  */
  
#include "stm32f4xx.h"
#include "./tim/bsp_general_tim.h"
#include "tim_adc_dma_fft.h"
#include "./systick/bsp_SysTick.h"


/**
  * @brief  ������
  * @param  ��
  * @retval ��
  */
int main(void)
{
  /* ��ʼ��ͨ�ö�ʱ������ͬ��PWM��� */
	/* ʵ�֣�TIM2�����¼�����ʱ���ʹ����ź�����TIM3���� */
	/*       TIM3�����¼�����ʱ���ʹ����ź�����TIM4���� */
//	TIMx_Configuration();
  	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//����ϵͳ�ж����ȼ�����2

	// 修复时钟配置后，逐步测试初始化
	uart_init(115200);
	printf("System starting with 8MHz HSE...\r\n");
	printf("UART working correctly\r\n");

	SysTick_Init();
	printf("SysTick initialized\r\n");

	// 现在尝试完整的Data_Init
	printf("Starting Data_Init...\r\n");
	Data_Init();
	printf("Data_Init completed successfully\r\n");

	printf("Starting TIM3...\r\n");
	TIM_Cmd(TIM3,ENABLE);
	printf("TIM3 enabled\r\n");

	printf("All initialization completed! System ready.\r\n");

  while(1)
  {
		// 正常运行，不需要频繁打印
		// printf("Main loop running...\r\n");
		// for(int i=0; i<1000000; i++);
  }
}



/*********************************************END OF FILE**********************/


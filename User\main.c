/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V1.0
  * @date    2015-xx-xx
  * @brief   ͨ�ö�ʱ������ͬ��
  ******************************************************************************
  * @attention
  *
  * ʵ��ƽ̨:Ұ��  STM32 F429 ������
  * ��̳    :http://www.firebbs.cn
  * �Ա�    :https://fire-stm32.taobao.com
  *
  ******************************************************************************
  */
  
#include "stm32f4xx.h"
#include "./tim/bsp_general_tim.h"
#include "tim_adc_dma_fft.h"
#include "./systick/bsp_SysTick.h"


/**
  * @brief  ������
  * @param  ��
  * @retval ��
  */
int main(void) 
{	
  /* ��ʼ��ͨ�ö�ʱ������ͬ��PWM��� */
	/* ʵ�֣�TIM2�����¼�����ʱ���ʹ����ź�����TIM3���� */
	/*       TIM3�����¼�����ʱ���ʹ����ź�����TIM4���� */
//	TIMx_Configuration();
  	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//����ϵͳ�ж����ȼ�����2
	uart_init(115200);
	
	SysTick_Init();
	
	
	Data_Init();
	
  while(1)
  {   
//		Delay_ms(3000);
		TIM_Cmd(TIM3,ENABLE);
  }
}



/*********************************************END OF FILE**********************/


/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V1.0
  * @date    2015-xx-xx
  * @brief   ͨ�ö�ʱ������ͬ��
  ******************************************************************************
  * @attention
  *
  * ʵ��ƽ̨:Ұ��  STM32 F429 ������
  * ��̳    :http://www.firebbs.cn
  * �Ա�    :https://fire-stm32.taobao.com
  *
  ******************************************************************************
  */
  
#include "stm32f4xx.h"
#include "./tim/bsp_general_tim.h"
#include "tim_adc_dma_fft.h"
#include "./systick/bsp_SysTick.h"


/**
  * @brief  ������
  * @param  ��
  * @retval ��
  */
int main(void)
{
  /* ��ʼ��ͨ�ö�ʱ������ͬ��PWM��� */
	/* ʵ�֣�TIM2�����¼�����ʱ���ʹ����ź�����TIM3���� */
	/*       TIM3�����¼�����ʱ���ʹ����ź�����TIM4���� */
//	TIMx_Configuration();
  	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//����ϵͳ�ж����ȼ�����2

	// 最简化测试 - 只初始化串口
	uart_init(115200);
	printf("System starting...\r\n");
	printf("UART working correctly\r\n");

	// 暂时注释掉可能导致HardFault的初始化
	/*
	SysTick_Init();
	printf("Step 2: SysTick initialized\r\n");

	Data_Init();
	printf("Data_Init completed\r\n");

	TIM_Cmd(TIM3,ENABLE);
	*/

	printf("Entering main loop\r\n");

  while(1)
  {
		printf("Main loop running...\r\n");
		// 简单延时
		for(int i=0; i<1000000; i++);
  }
}



/*********************************************END OF FILE**********************/


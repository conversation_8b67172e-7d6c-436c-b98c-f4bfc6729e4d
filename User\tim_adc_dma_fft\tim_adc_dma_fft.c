#include "tim_adc_dma_fft.h"
#include "usart.h"
#include "arm_math.h"  
#include "stm32f4xx.h"                  // Device header

//#include "oled.h"  
//#include "delay.h" 
#define sampledot  4096
#define FFT_LENGTH		4096		//1024点FFT
#define fft_arr 10                
#define fft_psc 90                   
const u32  fft_sample_freq=90000000/(fft_arr*fft_psc);  //fft采样频率 为信号的3到6倍   幅值最准确   
float fft_inputbuf[FFT_LENGTH*2];	//FFT输入数组
float fft_outputbuf[FFT_LENGTH];	//FFT输出数组
arm_cfft_radix4_instance_f32 scfft;  
u32 sampledata[sampledot]={0};//高16位保存adc2 pa5， 低16位保存adc1 pa6
float angel=0;
void Tim3_Init(u16 arr,u16 psc)
{
	TIM_TimeBaseInitTypeDef   TIM_TimeBaseInitstruct;          
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3,ENABLE);          
	TIM_TimeBaseInitstruct.TIM_Period=arr;   
    TIM_TimeBaseInitstruct.TIM_Prescaler=psc;
	TIM_TimeBaseInitstruct.TIM_CounterMode=TIM_CounterMode_Up;
	TIM_TimeBaseInitstruct.TIM_ClockDivision=TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitstruct);
	//TIM_ITConfig(TIM3,TIM_IT_Update,ENABLE);     
	TIM_SelectOutputTrigger(TIM3, TIM_TRGOSource_Update);	
	TIM_Cmd(TIM3,DISABLE);
}

void Adc_Init()
{
	GPIO_InitTypeDef  GPIO_InitStructure;
	ADC_CommonInitTypeDef ADC_CommonInitStructure;
	ADC_InitTypeDef       ADC_InitStructure;
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);
  	RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC2, ENABLE);	
	 GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2|GPIO_Pin_5;  //adc 1和2 的通道
	 GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
	 GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
	 GPIO_Init(GPIOA, &GPIO_InitStructure);
	 
	RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC1,ENABLE);	
	RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC1,DISABLE);
	RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC2,ENABLE);	
	RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC2,DISABLE); //重置
	ADC_CommonInitStructure.ADC_Mode = ADC_DualMode_InjecSimult;
    ADC_CommonInitStructure.ADC_TwoSamplingDelay =    ADC_TwoSamplingDelay_5Cycles;
    ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_2;
    ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;
    ADC_CommonInit(&ADC_CommonInitStructure);
    
	ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;	
    ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;  
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;	
    ADC_InitStructure.ADC_NbrOfConversion =1;  //通道数
	ADC_InitStructure.ADC_ExternalTrigConv=ADC_ExternalTrigConv_T3_TRGO;
    ADC_Init(ADC1, &ADC_InitStructure);
    ADC_Init(ADC2, &ADC_InitStructure);
    ADC_RegularChannelConfig(ADC2, ADC_Channel_5, 1, ADC_SampleTime_3Cycles);
	ADC_RegularChannelConfig(ADC1, ADC_Channel_2, 1, ADC_SampleTime_3Cycles);
		
   ADC_MultiModeDMARequestAfterLastTransferCmd(ENABLE); //多路转化完后触发dma 
	ADC_DMACmd(ADC1, ENABLE); 
	ADC_Cmd(ADC1, ENABLE);
    ADC_Cmd(ADC2, ENABLE);
}



void Dma_ADC_Init()
{
	
	
	DMA_InitTypeDef  DMA_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);
	DMA_DeInit(DMA2_Stream0);
	DMA_InitStructure.DMA_BufferSize= sampledot;
	DMA_InitStructure.DMA_Channel=DMA_Channel_0; 
	DMA_InitStructure.DMA_DIR=DMA_DIR_PeripheralToMemory;	
	DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Enable;         
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;	
	DMA_InitStructure.DMA_Memory0BaseAddr= (uint32_t)&sampledata ;//要存入的值	DMA_InitStructure.DMA_MemoryBurst=DMA_MemoryBurst_Single;
	DMA_InitStructure.DMA_MemoryDataSize= DMA_MemoryDataSize_Word;
	DMA_InitStructure.DMA_MemoryInc=DMA_MemoryInc_Enable;		
	DMA_InitStructure.DMA_Mode=DMA_Mode_Circular;
	DMA_InitStructure.DMA_PeripheralBaseAddr=(uint32_t)0x40012308; //adc地址
	DMA_InitStructure.DMA_PeripheralBurst=DMA_PeripheralBurst_Single;
	DMA_InitStructure.DMA_PeripheralDataSize=DMA_PeripheralDataSize_Word;
	DMA_InitStructure.DMA_PeripheralInc=DMA_PeripheralInc_Disable;
	DMA_InitStructure.DMA_Priority=DMA_Priority_High;
	
  DMA_Init(DMA2_Stream0, &DMA_InitStructure);
  DMA_ITConfig(DMA2_Stream0, DMA_IT_TC, ENABLE);
  DMA_Cmd(DMA2_Stream0, ENABLE);
	 
  NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream0_IRQn;  //DMA2_Stream0中断
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=0;  //抢占优先级1
  NVIC_InitStructure.NVIC_IRQChannelSubPriority =0;        //子优先级1
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;            //IRQ通道使能
  NVIC_Init(&NVIC_InitStructure);
}

//获取峰值
int fft_getpeak(float *inputx,float *input,float *output,u16 inlen,u8 x,u8 N,float y) //  intlen 输入数组长度，x寻找长度
{                                                                           
	int i,i2;
	u32 idex;  //不同于上一个函数中的，因为他们在不同的函数中被定义
	float datas;
	float sum;
	int outlen=0;
	for(i=0;i<inlen-x;i+=x)
	{
		arm_max_f32(input+i,x,&datas,&idex);   
		if( (input[i+idex]>=input[i+idex+1])&&(input[i+idex]>=input[i+idex-1])&&( (2*datas)/FFT_LENGTH )>y)   
		   {
			   sum=0;   
			   for(i2=i+idex-N;i2<i+idex+N;i2++)   
			   {
				   sum+=input[i2];          
			   }        
			   if(1.5*sum/(2*N)<datas)       
			   {                                                                                             
				     output[3*outlen+2] = atan2(inputx[2*(i+idex+1)+1],inputx[2*(i+idex+1)])*180/3.1415926f;				   
				     output[3*outlen+1] = 1.0*(2*datas)/FFT_LENGTH;   //计算幅度
					 output[3*outlen] = 1.0*fft_sample_freq*(i+idex+1)/FFT_LENGTH;//计算频率
					 outlen++;				   
			   }                                                                                               
               else continue;			   
		   }
			
		else continue;
		
	}
	return outlen;	
}


float freamp[50];//获取各次谐波频率和幅?

void DMA2_Stream0_IRQHandler(void)  
{
	u32 idex;	//用于将采集到的数据赋值给fft_inputbuf[2*idex]的计数	
    float zhiliu2,HZ2,amp2,phase2,zhiliu1,HZ1,amp1,phase1;
	u8 temp[40];
	int i;
	u16   freamplen; // freamp长度的一半
	if(DMA_GetITStatus(DMA2_Stream0, DMA_IT_TCIF0))  //判断DMA传输完成中断  
    {
		
		TIM_Cmd(TIM3,DISABLE);//失能时钟，进行计算			
		//adc2 pa5
		for(idex=0;idex<sampledot;idex++) //高16位fft，adc2 fft1 //sampledot==4096
		{			
			fft_inputbuf[2*idex]=(u16)(sampledata[idex]>>16)*(3.3/4096);    //生成输入信号实部
			fft_inputbuf[2*idex+1]=0;//虚部全部为0
		}
		arm_cfft_radix4_f32(&scfft,fft_inputbuf);  //fft运算
		arm_cmplx_mag_f32(fft_inputbuf,fft_outputbuf,FFT_LENGTH);	//把运算结果复数求模得幅值	
		freamplen=fft_getpeak(fft_inputbuf,fft_outputbuf+1,freamp,FFT_LENGTH/2,10,5,0.2);//寻找基波和谐波		
		zhiliu2=fft_outputbuf[0]/FFT_LENGTH;//直流 
		HZ2=freamp[0];//频率
		amp2=freamp[1];//幅度
		phase2=freamp[2];//相位
		freamp[0]=0;freamp[1]=0;freamp[2]=0;
		//adc1 pa6
		for(idex=0;idex<sampledot;idex++) //低16位fft ，adc1 fft2
		{
			 fft_inputbuf[2*idex]=(u16)(sampledata[idex])*(3.3/4096);    //生成输入信号实部
			fft_inputbuf[2*idex+1]=0;//虚部全部为0	
			
		}	
		arm_cfft_radix4_f32(&scfft,fft_inputbuf);  //fft运算
		arm_cmplx_mag_f32(fft_inputbuf,fft_outputbuf,FFT_LENGTH);	//把运算结果复数求模得幅值
//		for(i=0;i<FFT_LENGTH;i++)
//{
//printf("fft_outputbuf[%d]:%f\r\n",i,fft_outputbuf[i]);
//}
		freamplen=fft_getpeak(fft_inputbuf,fft_outputbuf+1,freamp,FFT_LENGTH/2,10,5,0.2); //寻找基波和谐波	
		zhiliu1=fft_outputbuf[0]/FFT_LENGTH;//直流      
		HZ1=freamp[0];//频率
		amp1=freamp[1];//幅度
		phase1=freamp[2];//相位
		freamp[0]=0;freamp[1]=0;freamp[2]=0;	
//		printf("zhiliu1:%.2f\r\n",zhiliu2);
//		printf("HZ1:%.2f\r\n",HZ2);
//		printf("amp1:%.2f\r\n",amp2);	
//		OLED_ShowString(5,5,"saHZ:",12);      
//		sprintf(temp,"%d",fft_sample_freq);    //fft采样频率   
//		OLED_ShowString(30,5,temp,12);	
//		OLED_ShowString(5,20,"zl:",12);
//		sprintf(temp," %.2f",zhiliu1); //直流分量
//		OLED_ShowString(20,20,temp,12);
//		OLED_ShowString(5,35,"HZ:",12);   //频率
//		sprintf(temp,"%.2f   ",HZ1);
//		OLED_ShowString(30,35,temp,12);
//		OLED_ShowString(5,50,"amp:",12);  //幅值  
//		sprintf(temp,"%.2f",amp1); 
//		OLED_ShowString(30,50,temp,12);

	angel=phase2-phase1;
	if(angel>180) angel=angel-180;
	if(angel<-180) angel=angel+180;

		// 打印相位差测量结果
		printf("相位差: %.2f 度\r\n", angel);
		printf("通道1(PA6) - 频率:%.2fHz 幅度:%.2f 相位:%.2f度\r\n", HZ1, amp1, phase1);
		printf("通道2(PA5) - 频率:%.2fHz 幅度:%.2f 相位:%.2f度\r\n", HZ2, amp2, phase2);
		printf("----------------------------------------\r\n");

//		OLED_ShowString(60,20,"ag:",12);
//		sprintf(temp,"%.2f    ",angel);
//		OLED_ShowString(75,20,temp,12);
//		OLED_Refresh_Gram();
 // printf("取样频率%d\r\n",fft_sample_freq);
		//printf("直流分量%f\r\n",fft_outputbuf[0]/FFT_LENGTH);
		for(idex=0;idex<freamplen;idex++)
			{
//				printf("HZ:%.2f   amplitude:%.2f 相位：%.2f \r\n",freamp[3*idex],freamp[3*idex+1],freamp[3*idex+2]);//没有求解出来
//				//printf("HZ:%.2f   amplitude:%.2f \r\n",freamp[3*idex],freamp[3*idex+1]);
			}	
		//TIM_Cmd(TIM3,ENABLE);//进行下一次读取           
		DMA_ClearITPendingBit(DMA2_Stream0, DMA_IT_TCIF0);
		
	}	
}

void Data_Init()
{
	u32 idex;
	float temp;	
	Adc_Init();
	Dma_ADC_Init();
	uart_init(115200);
	arm_cfft_radix4_init_f32(&scfft,FFT_LENGTH,0,1);//初始化scfft结构体，设定FFT相关参数     //FFT_LENGTH 4096
	Tim3_Init(fft_arr-1,fft_psc-1);
}

//获取峰值
//int fft_getpeak(float *inputx,float *input,float *output,u16 inlen,u8 x,u8 N,float y) //  intlen 输入数组长度，x寻找长度
//{                                                                           
//	int i,i2;
//	u32 idex;  //不同于上一个函数中的，因为他们在不同的函数中被定义
//	float datas;
//	float sum;
//	int outlen=0;
//	for(i=0;i<inlen-x;i+=x)
//	{
//		arm_max_f32(input+i,x,&datas,&idex);   
//		if( (input[i+idex]>=input[i+idex+1])&&(input[i+idex]>=input[i+idex-1])&&( (2*datas)/FFT_LENGTH )>y)   
//		   {
//			   sum=0;   
//			   for(i2=i+idex-N;i2<i+idex+N;i2++)   
//			   {
//				   sum+=input[i2];          
//			   }        
//			   if(1.5*sum/(2*N)<datas)       
//			   {                                                                                             
//				     output[3*outlen+2] = atan2(inputx[2*(i+idex+1)+1],inputx[2*(i+idex+1)])*180/3.1415926f;				   
//				     output[3*outlen+1] = 1.0*(2*datas)/FFT_LENGTH;   //计算幅度
//					 output[3*outlen] = 1.0*fft_sample_freq*(i+idex+1)/FFT_LENGTH;//计算频率
//					 outlen++;				   
//			   }                                                                                               
//               else continue;			   
//		   }
//			
//		else continue;
//		
//	}
//	return outlen;	
//}
